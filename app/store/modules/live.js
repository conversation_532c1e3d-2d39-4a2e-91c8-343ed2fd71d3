const state = {
    messages: [
      { id: 1, user: '用户1', text: '这个产品看起来不错！', avatar: 'https://nocode.meituan.com/photo/search?keyword=avatar&width=100&height=100' },
      { id: 2, user: '用户2', text: '主播好漂亮啊', avatar: 'https://nocode.meituan.com/photo/search?keyword=avatar&width=100&height=100' }
    ],
    viewers: 12345,
    products: [
      { id: 1, name: '新款智能手机', price: 2999, image: 'https://nocode.meituan.com/photo/search?keyword=phone&width=400&height=300' },
      { id: 2, name: '无线耳机', price: 399, image: 'https://nocode.meituan.com/photo/search?keyword=earphone&width=400&height=300' }
    ],
    newProduct: {}
}

const mutations = {
	addMessage(state, message) {
		console.log('##addMessage')
	  state.messages = [...state.messages.slice(-7), message]
	},
	incrementViewers(state, count) {
	  state.viewers += count
	},
	addProduct(state, product) {
	  state.products = [...state.products, product]
	  state.newProduct = product
	}
}

const actions = {
    addMessage({ commit }, message) {
      commit('addMessage', message)
    },
    addProduct({ commit }, product) {
      commit('addProduct', product)
    }
  }


export default {
  state,
  mutations,
  actions
};
