<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音直播间</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/vue@2.7.14/dist/vue.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#FE2C55',
                        secondary: '#FFDD2D',
                        dark: '#121212',
                        'dark-light': '#232323',
                        'gray-light': '#F2F2F2',
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            }
            .bg-gradient-overlay {
                background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
            }
            .bg-gradient-overlay-top {
                background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
            }
            .scale-in {
                animation: scaleIn 0.3s ease-out forwards;
            }
            .scale-out {
                animation: scaleOut 0.3s ease-in forwards;
            }
        }

        @keyframes scaleIn {
            0% { transform: scale(0.8); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes scaleOut {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(0.8); opacity: 0; }
        }
    </style>
</head>
<body class="bg-dark text-white overflow-hidden h-screen">
    <div id="app" class="relative h-full w-full">
        <!-- 直播视频背景 -->
        <div class="absolute inset-0 z-0">
            <video class="object-cover w-full h-full" autoplay muted loop>
                <source src="https://assets.mixkit.co/videos/preview/mixkit-woman-dancing-at-a-nightclub-4340-large.mp4" type="video/mp4">
                您的浏览器不支持视频标签
            </video>
        </div>
        
        <!-- 顶部信息 -->
        <div class="absolute top-0 left-0 right-0 z-10 bg-gradient-overlay-top p-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="w-10 h-10 rounded-full overflow-hidden border-2 border-white/30">
                    <img src="https://picsum.photos/200/200?random=1" alt="主播头像" class="w-full h-full object-cover">
                </div>
                <div>
                    <h2 class="font-bold text-lg text-shadow">舞蹈达人小舞</h2>
                    <p class="text-xs text-white/80 text-shadow">正在直播 · 热舞派对</p>
                </div>
            </div>
            <div class="bg-black/50 rounded-full px-3 py-1 flex items-center text-sm">
                <i class="fa fa-user-o mr-1"></i>
                <span id="viewers-count">{{ viewersCount }}</span>
            </div>
        </div>
        
        <!-- 右侧商品购物车按钮 -->
        <div class="absolute bottom-20 right-4 z-20">
            <button id="cart-btn" @click="toggleCart" class="bg-primary/90 hover:bg-primary text-white w-12 h-12 rounded-full flex items-center justify-center shadow-lg transform transition-all duration-300 hover:scale-110">
                <i class="fa fa-shopping-cart text-xl"></i>
            </button>
        </div>
        
        <!-- 商品抽屉 -->
        <div id="product-drawer" :class="cartVisible ? 'translate-x-0' : 'translate-x-full'" class="fixed top-0 right-0 bottom-0 w-80 bg-dark-light z-30 shadow-xl transition-transform duration-300 ease-in-out overflow-y-auto">
            <div class="p-4 border-b border-gray-700 flex justify-between items-center">
                <h3 class="font-bold">直播间商品</h3>
                <button @click="toggleCart" class="text-gray-400 hover:text-white">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <div class="p-3 space-y-3">
                <div v-for="(product, index) in products" :key="index" class="bg-dark rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300 cursor-pointer" @click="showProductDialog(product)">
                    <div class="relative">
                        <img :src="product.image" :alt="product.name" class="w-full h-40 object-cover">
                        <div class="absolute top-2 right-2 bg-primary text-white text-xs px-2 py-1 rounded">热卖</div>
                    </div>
                    <div class="p-3">
                        <h4 class="font-medium text-sm mb-1 line-clamp-2">{{ product.name }}</h4>
                        <div class="flex justify-between items-center">
                            <span class="text-primary font-bold">¥{{ product.price }}</span>
                            <button class="bg-primary/90 hover:bg-primary text-white text-xs px-3 py-1 rounded-full transition-colors duration-300">
                                立即购买
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 遮罩层 -->
        <div id="overlay" v-if="cartVisible || productDialogVisible" @click="closeOverlay" class="fixed inset-0 bg-black/50 z-20 transition-opacity duration-300"></div>
        
        <!-- 底部互动区域 -->
        <div class="absolute bottom-0 left-0 right-0 z-10 bg-gradient-overlay p-4">
            <!-- 留言滚动区 -->
            <div id="messages-container" class="h-32 overflow-hidden mb-3 relative">
                <div id="messages" class="absolute bottom-0 left-0 right-0 space-y-2 scrollbar-hide">
                    <div v-for="(message, index) in messages" :key="index" class="bg-black/40 backdrop-blur-sm rounded-lg px-3 py-2 max-w-[80%] animate-fade-in">
                        <div class="flex items-center space-x-2">
                            <div class="w-6 h-6 rounded-full overflow-hidden">
                                <img :src="message.avatar" :alt="message.username + '头像'" class="w-full h-full object-cover">
                            </div>
                            <span class="text-secondary font-medium text-sm">{{ message.username }}:</span>
                            <span class="text-white text-sm">{{ message.content }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 输入框区域 -->
            <div class="flex space-x-2">
                <input 
                    v-model="newMessage"
                    @keyup.enter="sendMessage"
                    type="text" 
                    placeholder="说点什么..." 
                    class="flex-1 bg-black/50 backdrop-blur-sm border border-gray-700 rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary/50"
                >
                <button 
                    @click="sendMessage"
                    :disabled="!newMessage.trim()"
                    class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-full text-sm transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    发送
                </button>
            </div>
        </div>
        
        <!-- 商品对话框 -->
        <div id="product-dialog" v-if="productDialogVisible" class="fixed inset-0 z-40 flex items-center justify-center p-4">
            <div class="bg-dark-light rounded-xl shadow-2xl w-full max-w-md scale-in overflow-hidden" @click.stop>
                <div class="relative">
                    <img :src="currentProduct.image" :alt="currentProduct.name" class="w-full h-64 object-cover">
                    <div class="absolute top-3 right-3">
                        <button @click="closeProductDialog" class="bg-black/50 hover:bg-black/70 text-white w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-300">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                    <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                        <h3 class="text-xl font-bold text-white mb-1">{{ currentProduct.name }}</h3>
                        <p class="text-primary font-bold text-2xl">¥{{ currentProduct.price }}</p>
                    </div>
                </div>
                <div class="p-4">
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex items-center space-x-1">
                            <i class="fa fa-fire text-secondary"></i>
                            <span class="text-sm text-white/70">已售 {{ currentProduct.sold }}件</span>
                        </div>
                        <div class="flex items-center space-x-1">
                            <i class="fa fa-clock-o text-secondary"></i>
                            <span class="text-sm text-white/70">剩余库存 {{ currentProduct.stock }}</span>
                        </div>
                    </div>
                    <button @click="buyProduct" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 rounded-lg transition-colors duration-300 flex items-center justify-center">
                        <i class="fa fa-shopping-cart mr-2"></i>
                        立即购买
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                viewersCount: 12345,
                messages: [
                    { username: '用户12345', content: '主播跳得太好了！', avatar: 'https://picsum.photos/200/200?random=2' },
                    { username: '舞蹈爱好者', content: '这首歌叫什么名字呀？', avatar: 'https://picsum.photos/200/200?random=3' },
                    { username: '小粉丝', content: '主播好漂亮！', avatar: 'https://picsum.photos/200/200?random=4' },
                    { username: '路人甲', content: '666666', avatar: 'https://picsum.photos/200/200?random=5' },
                    { username: '热舞达人', content: '这个舞蹈难度好大啊', avatar: 'https://picsum.photos/200/200?random=6' },
                ],
                newMessage: '',
                cartVisible: false,
                products: [
                    { 
                        name: '抖音同款潮流舞蹈鞋', 
                        price: 199.9, 
                        image: 'https://picsum.photos/400/400?random=10',
                        sold: 128,
                        stock: 32
                    },
                    { 
                        name: '高腰弹力健身瑜伽裤', 
                        price: 89.9, 
                        image: 'https://picsum.photos/400/400?random=11',
                        sold: 245,
                        stock: 18
                    },
                    { 
                        name: '专业舞蹈训练服套装', 
                        price: 259.9, 
                        image: 'https://picsum.photos/400/400?random=12',
                        sold: 87,
                        stock: 45
                    },
                    { 
                        name: '无线蓝牙耳机运动版', 
                        price: 149.9, 
                        image: 'https://picsum.photos/400/400?random=13',
                        sold: 320,
                        stock: 9
                    }
                ],
                productDialogVisible: false,
                currentProduct: null
            },
            methods: {
                sendMessage() {
                    if (!this.newMessage.trim()) return;
                    
                    // 添加自己的消息
                    this.messages.push({
                        username: '我',
                        content: this.newMessage,
                        avatar: 'https://picsum.photos/200/200?random=1'
                    });
                    
                    // 限制消息数量
                    if (this.messages.length > 8) {
                        this.messages.shift();
                    }
                    
                    // 清空输入框
                    this.newMessage = '';
                    
                    // 模拟其他用户发送消息
                    this.simulateUserMessage();
                },
                simulateUserMessage() {
                    const usernames = ['观众' + Math.floor(Math.random() * 10000), '抖音用户' + Math.floor(Math.random() * 1000)];
                    const contents = [
                        '主播太棒了！', '666666', '这个舞蹈我也想学', '太厉害了！', 
                        '主播什么时候开下一场？', '好喜欢这首歌', '求背景音乐',
                        '已经关注了，期待下一次直播', '爱了爱了', '这身材绝了'
                    ];
                    
                    setTimeout(() => {
                        this.messages.push({
                            username: usernames[Math.floor(Math.random() * usernames.length)],
                            content: contents[Math.floor(Math.random() * contents.length)],
                            avatar: `https://picsum.photos/200/200?random=${Math.floor(Math.random() * 100)}`
                        });
                        
                        // 限制消息数量
                        if (this.messages.length > 8) {
                            this.messages.shift();
                        }
                    }, 2000 + Math.random() * 5000);
                },
                toggleCart() {
                    this.cartVisible = !this.cartVisible;
                    
                    // 更新观众人数动画
                    if (this.cartVisible) {
                        this.animateViewersCount();
                    }
                },
                animateViewersCount() {
                    // 模拟观众人数实时变化
                    const originalCount = this.viewersCount;
                    const maxChange = 50;
                    const duration = 2000; // 2秒
                    const interval = 100; // 0.1秒更新一次
                    const steps = duration / interval;
                    let step = 0;
                    
                    const timer = setInterval(() => {
                        step++;
                        if (step >= steps) {
                            clearInterval(timer);
                            this.viewersCount = originalCount + Math.floor(Math.random() * maxChange);
                            return;
                        }
                        
                        const change = Math.floor(Math.random() * 10) - 3; // -3到6之间的随机数
                        this.viewersCount += change;
                    }, interval);
                },
                showProductDialog(product) {
                    this.currentProduct = product;
                    this.productDialogVisible = true;
                    
                    // 添加到消息列表
                    this.messages.push({
                        username: '系统通知',
                        content: `主播正在讲解【${product.name}】，点击下方链接查看详情！`,
                        avatar: 'https://picsum.photos/200/200?random=system'
                    });
                    
                    // 限制消息数量
                    if (this.messages.length > 8) {
                        this.messages.shift();
                    }
                    
                    // 30秒后自动关闭
                    setTimeout(() => {
                        this.closeProductDialog();
                    }, 30000);
                },
                closeProductDialog() {
                    this.productDialogVisible = false;
                },
                buyProduct() {
                    // 模拟购买流程
                    alert(`已成功购买【${this.currentProduct.name}】！`);
                    this.closeProductDialog();
                },
                closeOverlay() {
                    this.cartVisible = false;
                    this.productDialogVisible = false;
                },
                scheduleProductPush() {
                    // 随机时间后推送商品
                    setTimeout(() => {
                        const randomProduct = this.products[Math.floor(Math.random() * this.products.length)];
                        this.showProductDialog(randomProduct);
                        
                        // 继续下一次推送
                        this.scheduleProductPush();
                    }, 15000 + Math.random() * 30000); // 15-45秒之间随机推送
                }
            },
            mounted() {
                // 定时增加观众人数
                setInterval(() => {
                    this.viewersCount += Math.floor(Math.random() * 10) + 1;
                }, 5000);
                
                // 模拟用户持续发送消息
                setInterval(() => {
                    this.simulateUserMessage();
                }, 5000 + Math.random() * 10000);
                
                // 模拟服务器推送商品消息
                this.scheduleProductPush();
            }
        });
        
        // 添加消息动画
        document.addEventListener('DOMContentLoaded', function() {
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                }
                .animate-fade-in {
                    animation: fadeIn 0.5s ease forwards;
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>