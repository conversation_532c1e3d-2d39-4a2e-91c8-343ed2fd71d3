<template>
  <view class="live-container">
    <!-- 直播视频背景 -->
    <view id="tcView" ref="tcView" class="video-background">
<!--      <video-->
<!--        class="live-video"-->
<!--        :src="videoSrc"-->
<!--        autoplay-->
<!--        muted-->
<!--        loop-->
<!--        object-fit="cover"-->
<!--        :show-center-play-btn="false"-->
<!--        :show-play-btn="false"-->
<!--        :controls="false"-->
<!--      >-->
<!--      </video>-->
    </view>

    <!-- 顶部信息区 -->
    <view class="top-info">
      <view class="live-info">
        <view class="avatar">
          <image :src="liveData.avatar" mode="aspectFill"></image>
        </view>
        <view class="info-text">
          <text class="live-title">{{ liveData.title }}</text>
          <text class="live-status">正在直播 · {{ liveData.category }}</text>
        </view>
      </view>
      <view class="viewer-count">
        <text class="iconfont icon-yonghu"></text>
        <text>{{ viewersCount }}</text>
      </view>
    </view>

    <!-- 底部互动区域 -->
    <view class="bottom-interaction" :style="{ bottom: (100 + keyboardHeight * 2) + 'rpx' }">
      <!-- 留言滚动区 -->
      <scroll-view
        class="messages-container"
        scroll-y
        :scroll-top="scrollTop"
        scroll-with-animation
        :scroll-into-view="scrollIntoView"
      >
        <view class="messages-list">
          <view
            v-for="(message, index) in displayMessages"
            :key="index"
            :id="'message-' + index"
            class="message-item"
            :class="{ 'fade-in': message.isNew }"
          >
            <view class="message-content">
              <image class="user-avatar" :src="message.avatar" mode="aspectFill"></image>
              <text class="username">{{ message.username }}:</text>
              <text class="content">{{ message.content }}</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 输入框区域 -->
      <view class="input-area">
        <input
          v-model="newMessage"
          class="message-input"
          placeholder="说点什么..."
          @confirm="sendMessage"
          @focus="onInputFocus"
          @blur="onInputBlur"
          confirm-type="send"
        />
        <button
          class="send-button"
          :class="{ 'disabled': !newMessage.trim() }"
          @click="sendMessage"
          :disabled="!newMessage.trim()"
        >
          发送
        </button>

        <!-- 购物车按钮移到输入框右侧 -->
        <view class="cart-button" @click="toggleCart">
          <text class="iconfont icon-gouwuche"></text>
        </view>
      </view>
    </view>

    <!-- 商品抽屉 -->
    <view
      class="product-drawer"
      :class="{ 'show': cartVisible }"
    >
      <view class="drawer-header">
        <text class="drawer-title">直播间商品</text>
        <text class="iconfont icon-guanbi" @click="toggleCart"></text>
      </view>
      <scroll-view class="drawer-content" scroll-y="true">
        <view
          v-for="(product, index) in products"
          :key="index"
          class="product-item"
          @click="showProductDialog(product)"
        >
          <view class="product-image">
            <image :src="product.image" mode="aspectFill"></image>
            <view class="hot-tag">热卖</view>
          </view>
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-desc">{{ product.description }}</text>
            <view class="product-bottom">
              <text class="product-price">¥{{ product.price }}</text>
              <button class="buy-button" @click.stop="buyProduct(product)">去购买</button>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 遮罩层 -->
    <view
      v-if="cartVisible || productDialogVisible"
      class="overlay"
      @click="closeOverlay"
    ></view>

    <!-- 商品对话框 -->
    <view
      v-if="productDialogVisible"
      class="product-dialog"
    >
      <view class="dialog-content" @click.stop>
        <view class="dialog-image">
          <image :src="currentProduct.image" mode="aspectFill"></image>
          <view class="close-button" @click="closeProductDialog">
            <text class="iconfont icon-guanbi"></text>
          </view>
        </view>
        <view class="dialog-info">
          <text class="dialog-title">{{ currentProduct.name }}</text>
          <text class="dialog-price">¥{{ currentProduct.price }}</text>
          <view class="dialog-stats">
            <text class="sold-count">已售 {{ currentProduct.sold }}件</text>
            <text class="stock-count">库存 {{ currentProduct.stock }}件</text>
          </view>
          <button class="dialog-buy-button" @click="buyProduct(currentProduct)">
            <text class="iconfont icon-gouwuche"></text>
            立即购买
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LiveRoom',
  data() {
    return {
      // 直播数据
      liveData: {
        title: '抖音直播间',
        category: '热舞派对',
        avatar: 'https://img0.baidu.com/it/u=1195238836,824290916&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=500'
      },
      videoSrc: 'https://assets.mixkit.co/videos/preview/mixkit-woman-dancing-at-a-nightclub-4340-large.mp4',

      // 观众数据
      viewersCount: 12345,

      // 消息数据
      messages: [
        { username: '用户**', content: '111有优惠券吗？', avatar: 'https://picsum.photos/200/200?random=2', isNew: false },
        { username: '用户**', content: '已下单，等着收货', avatar: 'https://picsum.photos/200/200?random=3', isNew: false },
        { username: '用户**', content: '哪里可以买到？？', avatar: 'https://picsum.photos/200/200?random=4', isNew: false },
        { username: '用户**', content: '666666', avatar: 'https://picsum.photos/200/200?random=5', isNew: false },
        { username: '用户**', content: '价格多少？', avatar: 'https://picsum.photos/200/200?random=6', isNew: false }
      ],
      newMessage: '',
      scrollTop: 0,
      scrollIntoView: '',

      // 购物车和商品数据
      cartVisible: false,
      products: [
        {
          name: '抖音同款潮流舞蹈鞋',
          description: '时尚舒适，专业舞蹈必备',
          price: 199.9,
          image: 'https://picsum.photos/400/400?random=10',
          sold: 128,
          stock: 32
        },
        {
          name: '高腰弹力健身瑜伽裤',
          description: '弹性好，透气舒适',
          price: 89.9,
          image: 'https://picsum.photos/400/400?random=11',
          sold: 245,
          stock: 18
        },
        {
          name: '专业舞蹈训练服套装',
          description: '专业设计，舞者首选',
          price: 259.9,
          image: 'https://picsum.photos/400/400?random=12',
          sold: 87,
          stock: 45
        },
        {
          name: '无线蓝牙耳机运动版',
          description: '高音质，运动防汗',
          price: 149.9,
          image: 'https://picsum.photos/400/400?random=13',
          sold: 320,
          stock: 9
        }
      ],

      // 商品对话框
      productDialogVisible: false,
      currentProduct: null,

      // 定时器
      messageTimer: null,
      viewerTimer: null,
      productPushTimer: null,

      // 键盘高度
      keyboardHeight: 0
    }
  },
  computed: {
    displayMessages() {
      // 只显示最新的8条消息
      return this.messages.slice(-8);
    }
  },
  methods: {
    // 发送消息
    sendMessage() {
      if (!this.newMessage.trim()) return;

      // 添加用户消息
      this.messages.push({
        username: '我',
        content: this.newMessage.trim(),
        avatar: 'https://picsum.photos/200/200?random=1',
        isNew: true
      });

      // 清空输入框
      this.newMessage = '';

      // 滚动到底部
      this.scrollToBottom();

      // 模拟其他用户回复
      this.simulateUserMessage();
    },

    // 模拟用户发送消息
    simulateUserMessage() {
      const usernames = ['观众' + Math.floor(Math.random() * 10000), '抖音用户' + Math.floor(Math.random() * 1000)];
      const contents = [
        '主播太棒了！', '666666', '这个舞蹈我也想学', '太厉害了！',
        '主播什么时候开下一场？', '好喜欢这首歌', '求背景音乐',
        '已经关注了，期待下一次直播', '爱了爱了', '这身材绝了'
      ];

      setTimeout(() => {
        this.messages.push({
          username: usernames[Math.floor(Math.random() * usernames.length)],
          content: contents[Math.floor(Math.random() * contents.length)],
          avatar: `https://picsum.photos/200/200?random=${Math.floor(Math.random() * 100)}`,
          isNew: true
        });

        this.scrollToBottom();

        // 移除isNew标记
        setTimeout(() => {
          this.messages.forEach(msg => msg.isNew = false);
        }, 500);
      }, 2000 + Math.random() * 5000);
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const lastIndex = this.displayMessages.length - 1;
        if (lastIndex >= 0) {
          this.scrollIntoView = 'message-' + lastIndex;
        }
      });
    },

    // 切换购物车显示
    toggleCart() {
      this.cartVisible = !this.cartVisible;
      if (this.cartVisible) {
        this.animateViewersCount();
      }
    },

    // 动画更新观众数
    animateViewersCount() {
      const originalCount = this.viewersCount;
      const maxChange = 50;
      const steps = 20;
      let step = 0;

      const timer = setInterval(() => {
        step++;
        if (step >= steps) {
          clearInterval(timer);
          this.viewersCount = originalCount + Math.floor(Math.random() * maxChange);
          return;
        }

        const change = Math.floor(Math.random() * 10) - 3;
        this.viewersCount += change;
      }, 100);
    },

    // 显示商品对话框
    showProductDialog(product) {
      this.currentProduct = product;
      this.productDialogVisible = true;

      // 添加系统通知
      this.messages.push({
        username: '系统通知',
        content: `主播正在讲解【${product.name}】，点击查看详情！`,
        avatar: 'https://picsum.photos/200/200?random=system',
        isNew: true
      });

      this.scrollToBottom();

      // 30秒后自动关闭
      setTimeout(() => {
        this.closeProductDialog();
      }, 30000);
    },

    // 关闭商品对话框
    closeProductDialog() {
      this.productDialogVisible = false;
      this.currentProduct = null;
    },

    // 购买商品
    buyProduct(product) {
      uni.showToast({
        title: `已成功购买【${product.name}】！`,
        icon: 'success',
        duration: 2000
      });
      this.closeProductDialog();
      this.cartVisible = false;
    },

    // 关闭遮罩
    closeOverlay() {
      this.cartVisible = false;
      this.productDialogVisible = false;
    },

    // 输入框获得焦点
    onInputFocus() {
      // 延迟一点时间，等待键盘弹出
      setTimeout(() => {
        // 获取系统信息，计算键盘高度
        uni.getSystemInfo({
          success: (res) => {
            // 估算键盘高度（通常为屏幕高度的1/3左右）
            this.keyboardHeight = res.windowHeight * 0.4;
          }
        });
      }, 300);
    },

    // 输入框失去焦点
    onInputBlur() {
      setTimeout(() => {
        this.keyboardHeight = 0;
      }, 100);
    },

    // 随机推送商品
    scheduleProductPush() {
      this.productPushTimer = setTimeout(() => {
        const randomProduct = this.products[Math.floor(Math.random() * this.products.length)];
        this.showProductDialog(randomProduct);

        // 继续下一次推送
        this.scheduleProductPush();
      }, 15000 + Math.random() * 30000); // 15-45秒之间随机推送
    }
  },

  onLoad() {
    // 初始化滚动位置
    this.scrollToBottom();
  },

  onReady() {
    // 监听键盘高度变化
    uni.onKeyboardHeightChange && uni.onKeyboardHeightChange((res) => {
      this.keyboardHeight = res.height;
    });

    // 定时增加观众人数
    this.viewerTimer = setInterval(() => {
      this.viewersCount += Math.floor(Math.random() * 10) + 1;
    }, 5000);

    // 模拟用户持续发送消息
    this.messageTimer = setInterval(() => {
      this.simulateUserMessage();
    }, 5000 + Math.random() * 10000);

    // 模拟服务器推送商品消息
    this.scheduleProductPush();


    // 开启直播
    const container = document.getElementById('tcView');
    const video = document.createElement('video');
    video.id = 'player-container-id';
    video.width = container.clientWidth;
    video.height = container.clientHeight;
    video.setAttribute('preload', 'auto');
    video.setAttribute('playsinline', 'true');
    video.setAttribute('webkit-playsinline', 'true');
    container.appendChild(video);

    const player = TCPlayer('player-container-id', {
      sources: [{
        src: 'webrtc://live.aimaibumai.com/live/111222',
      }],
      controls: false,
      autoplay: true,
      licenseUrl: 'https://license.vod2.myqcloud.com/license/v2/1251108285_1/v_cube.license'
    });

    console.log(player)
  },

  onUnload() {
    // 清理定时器
    if (this.messageTimer) {
      clearInterval(this.messageTimer);
    }
    if (this.viewerTimer) {
      clearInterval(this.viewerTimer);
    }
    if (this.productPushTimer) {
      clearTimeout(this.productPushTimer);
    }
  }
}
</script>

<style lang="scss" scoped>
.live-container {
  position: relative;
  width: 100vw;
  height: calc(100vh - 100rpx); // 减去tabbar高度
  overflow: hidden;
  background-color: #000;
}

// 视频背景
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;

  .live-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 顶部信息区
.top-info {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  padding: 60rpx 30rpx 30rpx;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .live-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      overflow: hidden;
      border: 4rpx solid rgba(255, 255, 255, 0.3);
      margin-right: 20rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .info-text {
      display: flex;
      flex-direction: column;

      .live-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #fff;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
        margin-bottom: 8rpx;
      }

      .live-status {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
      }
    }
  }

  .viewer-count {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50rpx;
    padding: 16rpx 24rpx;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10rpx);

    .iconfont {
      font-size: 28rpx;
      color: #fff;
      margin-right: 8rpx;
    }

    text {
      font-size: 28rpx;
      color: #fff;
      font-weight: 500;
    }
  }
}



// 底部互动区域
.bottom-interaction {
  position: fixed;
  bottom: 100rpx; // tabbar高度
  left: 0;
  right: 0;
  z-index: 10;
  padding: 30rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  transition: bottom 0.3s ease; // 键盘弹出时的过渡动画

  .messages-container {
    height: 200rpx;
    margin-bottom: 20rpx;
    max-height: 200rpx;

    .messages-list {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      min-height: 240rpx;

      .message-item {
        margin-bottom: 16rpx;
        animation: fadeInUp 0.5s ease;

        &.fade-in {
          animation: fadeInUp 0.5s ease;
        }

        .message-content {
          background: rgba(0, 0, 0, 0.4);
          backdrop-filter: blur(10rpx);
          border-radius: 20rpx;
          padding: 16rpx 20rpx;
          max-width: 80%;
          display: flex;
          align-items: center;

          .user-avatar {
            width: 48rpx;
            height: 48rpx;
            border-radius: 50%;
            margin-right: 16rpx;
            flex-shrink: 0;
          }

          .username {
            font-size: 26rpx;
            color: #FFDD2D;
            font-weight: 500;
            margin-right: 8rpx;
            flex-shrink: 0;
          }

          .content {
            font-size: 26rpx;
            color: #fff;
            line-height: 1.4;
            word-break: break-all;
          }
        }
      }
    }
  }

  .input-area {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .message-input {
      flex: 1;
      background: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(10rpx);
      border: 2rpx solid rgba(255, 255, 255, 0.2);
      border-radius: 50rpx;
      padding: 24rpx 32rpx;
      font-size: 28rpx;
      color: #fff;
      height: 96rpx; // 与发送按钮高度一致
      line-height: 48rpx; // 文本垂直居中
      box-sizing: border-box;

      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .send-button {
      background: #E93323;
      color: #fff;
      border: none;
      border-radius: 50rpx;
      padding: 0 32rpx;
      font-size: 28rpx;
      font-weight: 500;
      transition: all 0.3s ease;
      height: 96rpx; // 与输入框高度一致
      line-height: 96rpx; // 文本垂直居中
      box-sizing: border-box;

      &.disabled {
        background: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.5);
      }

      &:not(.disabled):active {
        transform: scale(0.95);
        background: #d42c1f;
      }
    }

    // 购物车按钮（移到输入框右侧）
    .cart-button {
      width: 96rpx;
      height: 96rpx;
      background: rgba(233, 51, 35, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8rpx 24rpx rgba(233, 51, 35, 0.4);
      transition: all 0.3s ease;
      margin-left: 10rpx;

      &:active {
        transform: scale(0.95);
      }

      .iconfont {
        font-size: 48rpx;
        color: #fff;
      }
    }
  }
}

// 商品抽屉
.product-drawer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 600rpx;
  background: #232323;
  z-index: 30;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  box-shadow: -8rpx 0 24rpx rgba(0, 0, 0, 0.3);

  &.show {
    transform: translateX(0);
  }

  .drawer-header {
    padding: 30rpx;
    border-bottom: 2rpx solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .drawer-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #fff;
    }

    .iconfont {
      font-size: 36rpx;
      color: #999;
      padding: 10rpx;
    }
  }

  .drawer-content {
    height: calc(100vh - 120rpx);
    padding: 20rpx;

    .product-item {
      background: #121212;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      overflow: hidden;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      .product-image {
        position: relative;
        height: 300rpx;

        image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .hot-tag {
          position: absolute;
          top: 16rpx;
          right: 16rpx;
          background: #E93323;
          color: #fff;
          font-size: 20rpx;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
        }
      }

      .product-info {
        padding: 24rpx;

        .product-name {
          font-size: 28rpx;
          font-weight: 500;
          color: #fff;
          margin-bottom: 8rpx;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-desc {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 16rpx;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .product-price {
            font-size: 32rpx;
            font-weight: bold;
            color: #E93323;
          }

          .buy-button {
            background: rgba(233, 51, 35, 0.9);
            color: #fff;
            border: none;
            border-radius: 50rpx;
            padding: 16rpx 24rpx;
            font-size: 24rpx;
            transition: all 0.3s ease;

            &:active {
              transform: scale(0.95);
              background: #d42c1f;
            }
          }
        }
      }
    }
  }
}

// 遮罩层
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 20;
  transition: opacity 0.3s ease;
}

// 商品对话框
.product-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 40;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;

  .dialog-content {
    background: #232323;
    border-radius: 24rpx;
    overflow: hidden;
    width: 100%;
    max-width: 600rpx;
    box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.6);
    animation: scaleIn 0.3s ease;

    .dialog-image {
      position: relative;
      height: 400rpx;

      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .close-button {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        width: 60rpx;
        height: 60rpx;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10rpx);

        .iconfont {
          font-size: 32rpx;
          color: #fff;
        }
      }
    }

    .dialog-info {
      padding: 40rpx;

      .dialog-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #fff;
        margin-bottom: 16rpx;
        display: block;
        line-height: 1.4;
      }

      .dialog-price {
        font-size: 48rpx;
        font-weight: bold;
        color: #E93323;
        margin-bottom: 32rpx;
        display: block;
      }

      .dialog-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 40rpx;

        .sold-count,
        .stock-count {
          font-size: 26rpx;
          color: #999;
          display: flex;
          align-items: center;

          &::before {
            content: '';
            width: 8rpx;
            height: 8rpx;
            background: #FFDD2D;
            border-radius: 50%;
            margin-right: 12rpx;
          }
        }
      }

      .dialog-buy-button {
        width: 100%;
        background: #E93323;
        color: #fff;
        border: none;
        border-radius: 16rpx;
        padding: 32rpx;
        font-size: 32rpx;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        .iconfont {
          font-size: 32rpx;
          margin-right: 16rpx;
        }

        &:active {
          transform: scale(0.98);
          background: #d42c1f;
        }
      }
    }
  }
}

// 动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>