<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播间测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: #fff;
            overflow: hidden;
        }
        
        .live-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .top-info {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
            padding: 30px 15px 15px;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .live-info {
            display: flex;
            align-items: center;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.3);
            margin-right: 10px;
            background: #ff6b6b;
        }
        
        .info-text {
            display: flex;
            flex-direction: column;
        }
        
        .live-title {
            font-size: 18px;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
            margin-bottom: 4px;
        }
        
        .live-status {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
        }
        
        .viewer-count {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 25px;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            backdrop-filter: blur(5px);
            font-size: 14px;
            font-weight: 500;
        }
        
        .cart-button {
            position: absolute;
            bottom: 160px;
            right: 15px;
            z-index: 20;
            width: 48px;
            height: 48px;
            background: #E93323;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(233, 51, 35, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 24px;
        }
        
        .cart-button:hover {
            transform: scale(1.1);
        }
        
        .bottom-interaction {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 10;
            padding: 15px;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
        }
        
        .messages-container {
            height: 120px;
            margin-bottom: 10px;
            overflow: hidden;
        }
        
        .message-item {
            margin-bottom: 8px;
            animation: fadeInUp 0.5s ease;
        }
        
        .message-content {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(5px);
            border-radius: 10px;
            padding: 8px 10px;
            max-width: 80%;
            display: flex;
            align-items: center;
            font-size: 13px;
        }
        
        .username {
            color: #FFDD2D;
            font-weight: 500;
            margin-right: 4px;
        }
        
        .input-area {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .message-input {
            flex: 1;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 12px 16px;
            font-size: 14px;
            color: #fff;
            outline: none;
        }
        
        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .send-button {
            background: #E93323;
            color: #fff;
            border: none;
            border-radius: 25px;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .send-button:hover {
            background: #d42c1f;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .success-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #4CAF50;
            padding: 20px 40px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            z-index: 1000;
            animation: scaleIn 0.3s ease;
        }
        
        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="live-container">
        <!-- 顶部信息区 -->
        <div class="top-info">
            <div class="live-info">
                <div class="avatar"></div>
                <div class="info-text">
                    <div class="live-title">舞蹈达人小舞</div>
                    <div class="live-status">正在直播 · 热舞派对</div>
                </div>
            </div>
            <div class="viewer-count">
                👥 <span id="viewers">12,345</span>
            </div>
        </div>
        
        <!-- 右下角购物车按钮 -->
        <div class="cart-button" onclick="showSuccess('购物车功能正常！')">
            🛒
        </div>
        
        <!-- 底部互动区域 -->
        <div class="bottom-interaction">
            <!-- 留言滚动区 -->
            <div class="messages-container" id="messages">
                <div class="message-item">
                    <div class="message-content">
                        <span class="username">用户12345:</span>
                        <span>主播跳得太好了！</span>
                    </div>
                </div>
                <div class="message-item">
                    <div class="message-content">
                        <span class="username">舞蹈爱好者:</span>
                        <span>这首歌叫什么名字呀？</span>
                    </div>
                </div>
                <div class="message-item">
                    <div class="message-content">
                        <span class="username">小粉丝:</span>
                        <span>主播好漂亮！</span>
                    </div>
                </div>
            </div>
            
            <!-- 输入框区域 -->
            <div class="input-area">
                <input 
                    type="text" 
                    class="message-input"
                    placeholder="说点什么..."
                    id="messageInput"
                    onkeypress="handleKeyPress(event)"
                />
                <button class="send-button" onclick="sendMessage()">发送</button>
            </div>
        </div>
    </div>

    <script>
        let viewerCount = 12345;
        
        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.textContent = message;
            document.body.appendChild(successDiv);
            
            setTimeout(() => {
                document.body.removeChild(successDiv);
            }, 2000);
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                addMessage('我', message);
                input.value = '';
                
                // 模拟其他用户回复
                setTimeout(() => {
                    const responses = ['666666', '太棒了！', '主播加油！', '好厉害！'];
                    const usernames = ['观众' + Math.floor(Math.random() * 1000)];
                    addMessage(usernames[0], responses[Math.floor(Math.random() * responses.length)]);
                }, 1000 + Math.random() * 2000);
            }
        }
        
        function addMessage(username, content) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-item';
            messageDiv.innerHTML = `
                <div class="message-content">
                    <span class="username">${username}:</span>
                    <span>${content}</span>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            
            // 保持最多8条消息
            const messages = messagesContainer.children;
            if (messages.length > 8) {
                messagesContainer.removeChild(messages[0]);
            }
            
            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // 模拟观众数量变化
        setInterval(() => {
            viewerCount += Math.floor(Math.random() * 10) + 1;
            document.getElementById('viewers').textContent = viewerCount.toLocaleString();
        }, 5000);
        
        // 模拟自动消息
        setInterval(() => {
            const autoMessages = [
                '主播太棒了！', '这个舞蹈我也想学', '666666', 
                '主播什么时候开下一场？', '好喜欢这首歌', '已经关注了'
            ];
            const usernames = ['观众' + Math.floor(Math.random() * 10000)];
            addMessage(usernames[0], autoMessages[Math.floor(Math.random() * autoMessages.length)]);
        }, 3000 + Math.random() * 5000);
        
        console.log('✅ 直播间页面测试版本加载成功！');
        console.log('✅ 所有功能模块已实现：');
        console.log('  - 视频背景（模拟）');
        console.log('  - 顶部信息区');
        console.log('  - 实时观众数');
        console.log('  - 消息滚动区');
        console.log('  - 输入框和发送');
        console.log('  - 购物车按钮');
        console.log('  - 响应式设计');
    </script>
</body>
</html>
