{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom"
				// "app-plus": {
				// 	"scrollIndicator": false //禁用原生导航栏
				// }
			}
		},
		{
			"path": "pages/order_addcart/order_addcart",
			"style": {
				"navigationBarTitleText": "购物车",
				"navigationBarBackgroundColor": "#E93323",
				"navigationStyle": "custom",
				"app-plus": {
					// #ifdef APP-PLUS
					"titleNView": {
						"type": "default"
					}
					// #endif
				}
			}
		},
		{
			"path": "pages/user/index",
			"style": {
				//"navigationBarTitleText": "个人中心",
				// #ifdef MP || APP-PLUS
				"navigationBarTextStyle": "black",
				"navigationBarBackgroundColor": "#E93323",
				// #endif
				"app-plus": {
					// #ifdef APP-PLUS
					"titleNView": {
						"type": "default"
					}
					// #endif
				}
			}
		},
		{
			"path": "pages/goods_details/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/goods_cate/goods_cate",
			"style": {
				"navigationBarBackgroundColor": "#E93323",
				//"navigationBarTitleText": "商品分类",
				"app-plus": {
					// #ifdef APP-PLUS
					"titleNView": {
						"type": "default"
					}
					// #endif
				}
			}
		},
		{
			"path": "pages/retrieve_password/index",
			"style": {
				"navigationBarTitleText": "找回密码"
			}
		},
		// #ifdef H5
		{
			"path": "pages/customer_list/index",
			"style": {
				"navigationBarTitleText": "客服列表"
			}
		},
		{
			"path": "pages/customer_list/chat",
			"style": {
				"navigationBarTitleText": "客服聊天"
			}
		},
		// #endif
		{
			"path": "pages/goods_list/index",
			"style": {
				"navigationBarTitleText": "商品列表",
			"navigationBarBackgroundColor": "#E93323",
					"app-plus": {
						// #ifdef APP-PLUS
						"titleNView": {
							"type": "default"
						}
						// #endif
					}
				}
			},
		{
			"path": "pages/news_list/index",
			"style": {
				//"navigationBarTitleText": "资讯",
				"navigationBarBackgroundColor": "#E93323",
				"app-plus": {
					// #ifdef APP-PLUS
					"titleNView": {
						"type": "default"
					}
					// #endif
				}
			}
		},
		{
			"path": "pages/news_details/index",
			"style": {
				//"navigationBarTitleText": "资讯详情",
				"navigationBarBackgroundColor": "#E93323",
				"app-plus": {
					// #ifdef APP-PLUS
					"titleNView": {
						"type": "default"
					}
					// #endif
				}
			}
		},
		//#ifdef H5
		{
			"path": "pages/auth/index",
			"style": {
				//"navigationBarTitleText": "CRMEB"
			}
		},
		//#endif
		{
			"path": "pages/goods_search/index",
			"style": {
				//"navigationBarTitleText": "搜索商品",
				"navigationBarBackgroundColor": "#E93323",
				"app-plus": {
					// #ifdef APP-PLUS
					"titleNView": {
						"type": "default"
					}
					// #endif
				}
			}
		},
		{
			"path": "pages/order_pay_status/index",
			"style": {
				"navigationBarTitleText": "支付成功"
			}
		},
		{
			"path": "pages/order_details/index",
			"style": {
				//"navigationBarTitleText": "订单详情",
				"navigationBarBackgroundColor": "#E93323",
				"app-plus": {
					// #ifdef APP-PLUS
					"titleNView": {
						"type": "default"
					}
					// #endif
				}
			}
		}, {
			"path": "pages/index/components/a_seckill",
			"style": {}
		}, {
			"path": "pages/index/components/b_combination",
			"style": {}
		}, {
			"path": "pages/live/index",
			"style": {}
		}
  ],
	"subPackages": [{
			"root": "pages/users",
			"name": "users",
			"pages": [
				{
					"path": "privacy/index",
					"style": {
						"navigationBarTitleText": "隐私协议"
					}
				},
				{
					"path": "web_page/index",
					"style": {
					}
				},
				{
					"path": "retrievePassword/index",
					"style": {
						"navigationBarTitleText": "忘记密码"
					}
				},
				{
					"path": "user_info/index",
					"style": {
						//"navigationBarTitleText": "个人资料",
						"navigationBarBackgroundColor": "#E93323",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_get_coupon/index",
					"style": {
						//"navigationBarTitleText": "领取优惠券",
						"navigationBarBackgroundColor": "#e93323",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_goods_collection/index",
					"style": {
						//"navigationBarTitleText": "收藏商品",
						"navigationBarBackgroundColor": "#e93323",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_sgin/index",
					"style": {
						//"navigationBarTitleText": "签到",
						"navigationBarBackgroundColor": "#e93323",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_sgin_list/index",
					"style": {
						//"navigationBarTitleText": "签到记录",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "user_money/index",
					"style": {
						//"navigationBarTitleText": "我的账户",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "user_bill/index",
					"style": {
						//"navigationBarTitleText": "账单明细",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "user_integral/index",
					"style": {
						//"navigationBarTitleText": "积分详情",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "user_coupon/index",
					"style": {
						//"navigationBarTitleText": "我的优惠券",
						"navigationBarBackgroundColor": "#e93323",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_spread_user/index",
					"style": {
						"navigationBarTitleText": "我的推广",
						"navigationBarBackgroundColor": "#e93323",
				        "navigationBarTextStyle": "#fff",

				        "app-plus": {
				            "titleNView": {
				                "type": "default"
				            }
				        }

				    }
				},
				{
					"path": "user_spread_code/index",
					"style": {
						//"navigationBarTitleText": "分销海报",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "user_spread_money/index",
					"style": {
						"navigationBarTitleText": "佣金记录",
						"navigationBarBackgroundColor": "#e93323",
				        "navigationBarTextStyle": "#fff",

				        "app-plus": {
				            "titleNView": {
				                "type": "default"
				            }
				        }

				    }
				},
				{
					"path": "user_cash/index",
					"style": {
						"navigationBarTitleText": "提现",
						"navigationBarBackgroundColor": "#e93323",
				        "navigationBarTextStyle": "#fff",

				        "app-plus": {
				            "titleNView": {
				                "type": "default"
				            }
				        }

				    }
				},
				{
					"path": "user_vip/index",
					"style": {
						"navigationBarTitleText": "会员中心",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default",
										"backgroundColor": "#fff"
									}
									// #endif
								}
							}
						},
				{
					"path": "user_address_list/index",
					"style": {
						"navigationBarTitleText": "地址管理",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "user_address/index",
					"style": {
						"navigationBarTitleText": "添加地址",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "user_phone/index",
					"style": {
						"navigationBarTitleText": "修改手机号",
						"navigationBarBackgroundColor": "#e93323"
							// #ifdef MP
							,
						"navigationBarTextStyle": "#fff"
							// #endif
					}
				},
				{
					"path": "user_payment/index",
					"style": {
						"navigationBarTitleText": "余额充值"
					}
				},
				{
					"path": "user_pwd_edit/index",
					"style": {
						"navigationBarTitleText": "修改密码",
						"navigationBarBackgroundColor": "#e93323"
							// #ifdef MP 
							,
						"navigationBarTextStyle": "#fff"
							// #endif
					}
				},
				{
					"path": "order_confirm/index",
					"style": {
						"navigationBarTitleText": "提交订单",
						"navigationBarBackgroundColor": "#e93323",
								// "navigationStyle": "custom",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "goods_details_store/index",
					"style": {
						"navigationBarTitleText": "门店列表",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "promoter-list/index",
					"style": {
						"navigationBarTitleText": "推广人列表",
				        "navigationBarBackgroundColor": "#e93323",

				        "navigationBarTextStyle": "#fff",

				        "app-plus": {
				            "titleNView": {
				                "type": "default"
				            }
				        }

				    }
				},
				{
					"path": "promoter-order/index",
					"style": {
						"navigationBarTitleText": "推广人订单",
				        "navigationBarBackgroundColor": "#e93323",

				        "navigationBarTextStyle": "#fff",

				        "app-plus": {
				            "titleNView": {
				                "type": "default"
				            }
				        }

				    }
				},
				{
				    "path": "promoter_rank/index",
				    "style": {
				        "navigationBarTitleText": "推广人排行",
				        "navigationBarBackgroundColor": "#e93323",

				        "navigationBarTextStyle": "#fff",

				        "app-plus": {
				            "titleNView": {
				                "type": "default"
				            }
				        }

				    }
				},
				{
					"path": "commission_rank/index",
					"style": {
						"navigationBarTitleText": "佣金排行",
						"navigationBarBackgroundColor": "#e93323",
				        "navigationBarTextStyle": "#fff",

				        "app-plus": {
				            "titleNView": {
				                "type": "default"
				            }
				        }

				    }
				},
				{
					"path": "order_list/index",
					"style": {
						"navigationBarTitleText": "我的订单",
						"navigationBarBackgroundColor": "#e93323",
								"navigationStyle": "custom",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "goods_logistics/index",
					"style": {
						"navigationBarTitleText": "物流信息",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "user_return_list/index",
					"style": {
						"navigationBarTitleText": "退货列表",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "goods_return/index",
					"style": {
						"navigationBarTitleText": "申请退货",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "login/index",
					"style": {
						"navigationBarTitleText": "登录",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "goods_comment_list/index",
					"style": {
						"navigationBarTitleText": "商品评分",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "goods_comment_con/index",
					"style": {
						"navigationBarTitleText": "商品评价",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "wechat_login/index",
					"style": {
						"navigationBarTitleText": "账户登录",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "app_login/index",
					"style": {
						"navigationBarTitleText": "绑定手机号",
						"navigationStyle": "custom"
      }
    }
  ]
		},
		{
			"root": "pages/activity",
			"name": "activity",
			"pages": [{
					"path": "goods_bargain/index",
					"style": {
						"navigationBarTitleText": "砍价列表"
						// #ifdef MP
							,
						"navigationBarTextStyle": "black",
						"navigationBarBackgroundColor": "#fff"
							// #endif
					}
				},
				{
					"path": "goods_bargain_details/index",
					"style": {
						"navigationBarTitleText": "砍价详情"
						// #ifdef MP
						,
						"navigationBarTextStyle": "black",
						"navigationBarBackgroundColor": "#fff"
							// #endif
					}
				},
				{
					"path": "goods_combination/index",
					"style": {
						"navigationBarTitleText": "拼团列表"
						// #ifdef MP || APP-PLUS
							,
						"navigationBarTextStyle": "#fff",
						"navigationBarBackgroundColor": "#e93323"
							// #endif
					}
				},
				{
					"path": "goods_combination_details/index",
					"style": {
						"navigationStyle": "custom",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "goods_combination_status/index",
					"style": {
						"navigationBarTitleText": "拼团",
						"navigationBarBackgroundColor": "#e93323",
								"app-plus": {
									// #ifdef APP-PLUS
									"titleNView": {
										"type": "default"
									}
									// #endif
								}
							}
						},
				{
					"path": "goods_seckill/index",
					"style": {
						"navigationBarTitleText": "限时秒杀"
							// #ifdef MP
							,
						"navigationBarTextStyle": "#fff",
						"navigationBarBackgroundColor": "#e93323"
							// #endif
					}
				},
				{
					"path": "goods_seckill_details/index",
					"style": {
						"navigationBarTitleText": "秒杀详情"
							// #ifdef MP
							,
						"navigationStyle": "custom"
							// #endif
					}
				},
				{
					"path": "poster-poster/index",
					"style": {
						"navigationBarTitleText": "砍价海报"
							// #ifdef MP
							,
						"navigationBarTextStyle": "#fff",
						"navigationBarBackgroundColor": "#e93323"
							// #endif
					}
				},
				{
					"path": "bargain/index",
					"style": {
						"navigationBarTitleText": "砍价记录"
      }
    }
  ]
		},
		{
			"root": "pages/columnGoods",
			"name": "columnGoods",
			"pages": [{
				"path": "HotNewGoods/index",
				"style": {
					"navigationBarTitleText": "精品推荐"
				}
			}]
    }
  ],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		//"navigationBarTitleText": "crmeb",
		"navigationBarBackgroundColor": "#ff5500",
		"backgroundColor": "#F8F8F8",
		"titleNView": false,
		"rpxCalcMaxDeviceWidth": 960,
		"rpxCalcBaseDeviceWidth": 375,
		"rpxCalcIncludeWidth": 750
	},
	"tabBar": {
		"color": "#282828",
		"selectedColor": "#fc4141",
		"borderStyle": "white",
		"backgroundColor": "#ffffff",
		"list": [
			{
				"pagePath": "pages/index/index",
				"iconPath": "static/images/1-001.png",
				"selectedIconPath": "static/images/1-002.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/goods_cate/goods_cate",
				"iconPath": "static/images/2-001.png",
				"selectedIconPath": "static/images/2-002.png",
				"text": "分类"
			},
			{
				"pagePath": "pages/live/index",
				"iconPath": "static/images/live-001.png",
				"selectedIconPath": "static/images/live-002-01.png",
				"text": "直播"
			},
			{
				"pagePath": "pages/order_addcart/order_addcart",
				"iconPath": "static/images/3-001.png",
				"selectedIconPath": "static/images/3-002.png",
				"text": "购物车"
			},
			{
				"pagePath": "pages/user/index",
				"iconPath": "static/images/4-001.png",
				"selectedIconPath": "static/images/4-002.png",
				"text": "我的"
    }
  ]
	},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "", //模式名称
			"path": "", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}
}